"""
AI Agent Framework 配置管理器

提供统一的配置管理功能，支持配置的加载、保存、验证和热更新。
采用单例模式确保全局配置的一致性。
"""

import os
import threading
from pathlib import Path
from typing import Any, Dict, Optional, Union

from ai_agent_framework.core.config import FrameworkConfig
from ai_agent_framework.exceptions import ConfigurationError


class ConfigManager:
    """
    配置管理器
    
    采用单例模式的配置管理器，负责加载、保存和管理框架配置。
    支持从多种来源加载配置：环境变量、配置文件、代码设置等。
    """
    
    _instance: Optional["ConfigManager"] = None
    _lock = threading.Lock()
    
    def __new__(cls) -> "ConfigManager":
        """单例模式实现"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化配置管理器"""
        if hasattr(self, '_initialized'):
            return
            
        self._config: Optional[FrameworkConfig] = None
        self._config_path: Optional[Path] = None
        self._watchers: Dict[str, Any] = {}
        self._initialized = True
    
    def load_config(
        self,
        config_path: Optional[Union[str, Path]] = None,
        config_dict: Optional[Dict[str, Any]] = None,
        use_env: bool = True,
    ) -> FrameworkConfig:
        """
        加载配置
        
        Args:
            config_path: 配置文件路径
            config_dict: 配置字典
            use_env: 是否使用环境变量
            
        Returns:
            FrameworkConfig: 加载的配置
            
        Raises:
            ConfigurationError: 配置加载失败时抛出
        """
        try:
            if config_path:
                # 从文件加载配置
                self._config_path = Path(config_path)
                self._config = FrameworkConfig.load_from_file(self._config_path)
            elif config_dict:
                # 从字典加载配置
                self._config = FrameworkConfig(**config_dict)
            elif use_env:
                # 从环境变量加载配置
                self._config = FrameworkConfig()
            else:
                # 使用默认配置
                from ai_agent_framework.core.config import create_default_config
                self._config = create_default_config()
            
            return self._config
            
        except Exception as e:
            raise ConfigurationError(
                f"配置加载失败: {str(e)}",
                error_code="CONFIG_LOAD_ERROR",
                cause=e
            )
    
    def get_config(self) -> FrameworkConfig:
        """
        获取当前配置
        
        Returns:
            FrameworkConfig: 当前配置
            
        Raises:
            ConfigurationError: 配置未加载时抛出
        """
        if self._config is None:
            raise ConfigurationError(
                "配置未加载，请先调用 load_config() 方法",
                error_code="CONFIG_NOT_LOADED"
            )
        return self._config
    
    def save_config(self, config_path: Optional[Union[str, Path]] = None) -> None:
        """
        保存配置到文件
        
        Args:
            config_path: 配置文件路径，None时使用加载时的路径
            
        Raises:
            ConfigurationError: 保存失败时抛出
        """
        if self._config is None:
            raise ConfigurationError(
                "没有可保存的配置",
                error_code="NO_CONFIG_TO_SAVE"
            )
        
        try:
            path = Path(config_path) if config_path else self._config_path
            if path is None:
                raise ConfigurationError(
                    "未指定配置文件路径",
                    error_code="NO_CONFIG_PATH"
                )
            
            self._config.save_to_file(path)
            self._config_path = path
            
        except Exception as e:
            raise ConfigurationError(
                f"配置保存失败: {str(e)}",
                error_code="CONFIG_SAVE_ERROR",
                cause=e
            )
    
    def update_config(self, updates: Dict[str, Any]) -> None:
        """
        更新配置
        
        Args:
            updates: 要更新的配置项
            
        Raises:
            ConfigurationError: 更新失败时抛出
        """
        if self._config is None:
            raise ConfigurationError(
                "配置未加载，无法更新",
                error_code="CONFIG_NOT_LOADED"
            )
        
        try:
            # 创建新的配置实例
            current_dict = self._config.dict()
            current_dict.update(updates)
            self._config = FrameworkConfig(**current_dict)
            
        except Exception as e:
            raise ConfigurationError(
                f"配置更新失败: {str(e)}",
                error_code="CONFIG_UPDATE_ERROR",
                cause=e
            )
    
    def get_value(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            default: 默认值
            
        Returns:
            Any: 配置值
        """
        if self._config is None:
            return default
        
        try:
            value = self._config
            for part in key.split('.'):
                if hasattr(value, part):
                    value = getattr(value, part)
                elif isinstance(value, dict) and part in value:
                    value = value[part]
                else:
                    return default
            return value
        except Exception:
            return default
    
    def set_value(self, key: str, value: Any) -> None:
        """
        设置配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            value: 配置值
            
        Raises:
            ConfigurationError: 设置失败时抛出
        """
        if self._config is None:
            raise ConfigurationError(
                "配置未加载，无法设置值",
                error_code="CONFIG_NOT_LOADED"
            )
        
        try:
            # 将配置转换为字典进行更新
            config_dict = self._config.dict()
            
            # 处理嵌套键
            keys = key.split('.')
            current = config_dict
            
            for k in keys[:-1]:
                if k not in current:
                    current[k] = {}
                current = current[k]
            
            current[keys[-1]] = value
            
            # 重新创建配置实例
            self._config = FrameworkConfig(**config_dict)
            
        except Exception as e:
            raise ConfigurationError(
                f"配置值设置失败: {str(e)}",
                error_code="CONFIG_SET_ERROR",
                cause=e
            )
    
    def validate_config(self) -> bool:
        """
        验证当前配置
        
        Returns:
            bool: 配置是否有效
        """
        if self._config is None:
            return False
        
        try:
            # 尝试重新创建配置实例来验证
            FrameworkConfig(**self._config.dict())
            return True
        except Exception:
            return False
    
    def reload_config(self) -> FrameworkConfig:
        """
        重新加载配置
        
        Returns:
            FrameworkConfig: 重新加载的配置
            
        Raises:
            ConfigurationError: 重新加载失败时抛出
        """
        if self._config_path is None:
            raise ConfigurationError(
                "无法重新加载配置：未知的配置来源",
                error_code="UNKNOWN_CONFIG_SOURCE"
            )
        
        return self.load_config(self._config_path)
    
    def get_environment_config(self) -> Dict[str, str]:
        """
        获取环境变量配置
        
        Returns:
            Dict[str, str]: 环境变量字典
        """
        env_vars = {}
        prefix = "AI_AGENT_"
        
        for key, value in os.environ.items():
            if key.startswith(prefix):
                config_key = key[len(prefix):].lower()
                env_vars[config_key] = value
        
        return env_vars
    
    def is_debug_mode(self) -> bool:
        """
        检查是否为调试模式
        
        Returns:
            bool: 是否为调试模式
        """
        return self.get_value('debug', False)
    
    def get_model_config(self, model_name: Optional[str] = None):
        """
        获取模型配置
        
        Args:
            model_name: 模型名称
            
        Returns:
            ModelConfig: 模型配置
        """
        if self._config is None:
            raise ConfigurationError(
                "配置未加载",
                error_code="CONFIG_NOT_LOADED"
            )
        
        return self._config.get_model_config(model_name)
    
    def reset(self) -> None:
        """重置配置管理器"""
        self._config = None
        self._config_path = None
        self._watchers.clear()


# 全局配置管理器实例
config_manager = ConfigManager()
