"""
AI Agent Framework 工具注册表

提供工具的注册、发现、验证和管理功能。
支持动态工具加载、权限控制和并发执行管理。
"""

import asyncio
import inspect
from typing import Any, Dict, List, Optional, Set, Type

from ai_agent_framework.core.interfaces import ToolInterface
from ai_agent_framework.core.messages import ToolCall, ToolResult
from ai_agent_framework.exceptions import ToolError, ToolNotFoundError, ToolPermissionError
from ai_agent_framework.utils.logging_system import logging_system


class ToolRegistry:
    """
    工具注册表
    
    管理所有可用的工具，提供工具的注册、发现、验证和执行功能。
    支持工具权限控制、并发限制和动态加载。
    """
    
    def __init__(self):
        """初始化工具注册表"""
        self._tools: Dict[str, ToolInterface] = {}
        self._tool_permissions: Dict[str, Set[str]] = {}
        self._concurrent_executions: Dict[str, int] = {}
        self._max_concurrent: Dict[str, int] = {}
        self._logger = logging_system.get_logger("tool_registry")
        self._execution_lock = asyncio.Lock()
    
    def register_tool(
        self,
        tool: ToolInterface,
        permissions: Optional[Set[str]] = None,
        max_concurrent: int = 1,
    ) -> None:
        """
        注册工具
        
        Args:
            tool: 工具实例
            permissions: 所需权限集合
            max_concurrent: 最大并发执行数
            
        Raises:
            ToolError: 工具注册失败时抛出
        """
        try:
            tool_name = tool.name
            
            # 验证工具
            if not self._validate_tool(tool):
                raise ToolError(
                    f"工具验证失败: {tool_name}",
                    tool_name=tool_name,
                    error_code="TOOL_VALIDATION_FAILED"
                )
            
            # 注册工具
            self._tools[tool_name] = tool
            self._tool_permissions[tool_name] = permissions or set()
            self._max_concurrent[tool_name] = max_concurrent
            self._concurrent_executions[tool_name] = 0
            
            self._logger.info(f"工具已注册: {tool_name}")
            
        except Exception as e:
            raise ToolError(
                f"工具注册失败: {str(e)}",
                tool_name=getattr(tool, 'name', 'unknown'),
                error_code="TOOL_REGISTRATION_ERROR",
                cause=e
            )
    
    def unregister_tool(self, tool_name: str) -> bool:
        """
        注销工具
        
        Args:
            tool_name: 工具名称
            
        Returns:
            bool: 是否成功注销
        """
        if tool_name in self._tools:
            del self._tools[tool_name]
            del self._tool_permissions[tool_name]
            del self._max_concurrent[tool_name]
            del self._concurrent_executions[tool_name]
            
            self._logger.info(f"工具已注销: {tool_name}")
            return True
        
        return False
    
    def get_tool(self, tool_name: str) -> Optional[ToolInterface]:
        """
        获取工具实例
        
        Args:
            tool_name: 工具名称
            
        Returns:
            Optional[ToolInterface]: 工具实例，不存在时返回None
        """
        return self._tools.get(tool_name)
    
    def list_tools(self, permissions: Optional[Set[str]] = None) -> List[str]:
        """
        列出可用工具
        
        Args:
            permissions: 用户权限集合，用于过滤工具
            
        Returns:
            List[str]: 工具名称列表
        """
        if permissions is None:
            return list(self._tools.keys())
        
        available_tools = []
        for tool_name, required_perms in self._tool_permissions.items():
            if required_perms.issubset(permissions):
                available_tools.append(tool_name)
        
        return available_tools
    
    def get_tool_schemas(self, permissions: Optional[Set[str]] = None) -> List[Dict[str, Any]]:
        """
        获取工具schema列表
        
        Args:
            permissions: 用户权限集合
            
        Returns:
            List[Dict[str, Any]]: 工具schema列表
        """
        schemas = []
        available_tools = self.list_tools(permissions)
        
        for tool_name in available_tools:
            tool = self._tools[tool_name]
            schemas.append(tool.to_schema())
        
        return schemas
    
    async def execute_tool(
        self,
        tool_call: ToolCall,
        context: Optional[Dict[str, Any]] = None,
        permissions: Optional[Set[str]] = None,
    ) -> ToolResult:
        """
        执行工具
        
        Args:
            tool_call: 工具调用信息
            context: 执行上下文
            permissions: 用户权限集合
            
        Returns:
            ToolResult: 执行结果
            
        Raises:
            ToolNotFoundError: 工具不存在时抛出
            ToolPermissionError: 权限不足时抛出
            ToolError: 执行失败时抛出
        """
        tool_name = tool_call.name
        
        # 检查工具是否存在
        if tool_name not in self._tools:
            raise ToolNotFoundError(
                f"工具不存在: {tool_name}",
                tool_name=tool_name,
                error_code="TOOL_NOT_FOUND"
            )
        
        # 检查权限
        if permissions is not None:
            required_perms = self._tool_permissions[tool_name]
            if not required_perms.issubset(permissions):
                missing_perms = required_perms - permissions
                raise ToolPermissionError(
                    f"权限不足，缺少权限: {missing_perms}",
                    tool_name=tool_name,
                    error_code="INSUFFICIENT_PERMISSIONS",
                    context={"missing_permissions": list(missing_perms)}
                )
        
        # 检查并发限制
        async with self._execution_lock:
            current_concurrent = self._concurrent_executions[tool_name]
            max_concurrent = self._max_concurrent[tool_name]
            
            if current_concurrent >= max_concurrent:
                raise ToolError(
                    f"工具并发执行数已达上限: {current_concurrent}/{max_concurrent}",
                    tool_name=tool_name,
                    error_code="CONCURRENT_LIMIT_EXCEEDED"
                )
            
            self._concurrent_executions[tool_name] += 1
        
        try:
            tool = self._tools[tool_name]
            
            # 验证参数
            if not await tool.validate_arguments(tool_call.arguments):
                raise ToolError(
                    f"工具参数验证失败: {tool_name}",
                    tool_name=tool_name,
                    error_code="INVALID_ARGUMENTS",
                    context={"arguments": tool_call.arguments}
                )
            
            # 执行工具
            start_time = asyncio.get_event_loop().time()
            
            # 设置超时
            timeout = tool.timeout_seconds
            if timeout:
                result = await asyncio.wait_for(
                    tool.execute(tool_call.arguments, context),
                    timeout=timeout
                )
            else:
                result = await tool.execute(tool_call.arguments, context)
            
            execution_time = asyncio.get_event_loop().time() - start_time
            
            # 更新执行结果
            result.tool_call_id = tool_call.id
            result.execution_time = execution_time
            
            self._logger.info(
                f"工具执行成功: {tool_name}",
                extra={
                    "tool_name": tool_name,
                    "execution_time": execution_time,
                    "success": result.success,
                }
            )
            
            return result
            
        except asyncio.TimeoutError:
            raise ToolError(
                f"工具执行超时: {tool_name}",
                tool_name=tool_name,
                error_code="TOOL_TIMEOUT"
            )
        except Exception as e:
            self._logger.error(f"工具执行失败: {tool_name} - {str(e)}")
            raise ToolError(
                f"工具执行失败: {str(e)}",
                tool_name=tool_name,
                error_code="TOOL_EXECUTION_ERROR",
                cause=e
            )
        finally:
            # 减少并发计数
            async with self._execution_lock:
                self._concurrent_executions[tool_name] -= 1
    
    async def execute_tools_batch(
        self,
        tool_calls: List[ToolCall],
        context: Optional[Dict[str, Any]] = None,
        permissions: Optional[Set[str]] = None,
        max_concurrent: int = 5,
    ) -> List[ToolResult]:
        """
        批量执行工具
        
        Args:
            tool_calls: 工具调用列表
            context: 执行上下文
            permissions: 用户权限集合
            max_concurrent: 最大并发数
            
        Returns:
            List[ToolResult]: 执行结果列表
        """
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def execute_with_semaphore(tool_call: ToolCall) -> ToolResult:
            async with semaphore:
                return await self.execute_tool(tool_call, context, permissions)
        
        tasks = [execute_with_semaphore(call) for call in tool_calls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                # 创建失败的ToolResult
                processed_results.append(
                    ToolResult(
                        tool_call_id=tool_calls[i].id,
                        success=False,
                        error=str(result),
                    )
                )
            else:
                processed_results.append(result)
        
        return processed_results
    
    def _validate_tool(self, tool: ToolInterface) -> bool:
        """
        验证工具实现
        
        Args:
            tool: 工具实例
            
        Returns:
            bool: 是否有效
        """
        try:
            # 检查必需的属性和方法
            required_attrs = ['name', 'description', 'parameters_schema']
            for attr in required_attrs:
                if not hasattr(tool, attr):
                    self._logger.error(f"工具缺少必需属性: {attr}")
                    return False
            
            # 检查execute方法
            if not hasattr(tool, 'execute'):
                self._logger.error("工具缺少execute方法")
                return False
            
            # 检查execute方法是否为异步
            if not asyncio.iscoroutinefunction(tool.execute):
                self._logger.error("工具execute方法必须是异步方法")
                return False
            
            # 验证参数schema
            schema = tool.parameters_schema
            if not isinstance(schema, dict):
                self._logger.error("工具参数schema必须是字典类型")
                return False
            
            return True
            
        except Exception as e:
            self._logger.error(f"工具验证过程中出错: {str(e)}")
            return False
    
    def get_tool_info(self, tool_name: str) -> Optional[Dict[str, Any]]:
        """
        获取工具信息
        
        Args:
            tool_name: 工具名称
            
        Returns:
            Optional[Dict[str, Any]]: 工具信息
        """
        if tool_name not in self._tools:
            return None
        
        tool = self._tools[tool_name]
        return {
            "name": tool.name,
            "description": tool.description,
            "parameters_schema": tool.parameters_schema,
            "requires_confirmation": tool.requires_confirmation,
            "timeout_seconds": tool.timeout_seconds,
            "permissions": list(self._tool_permissions[tool_name]),
            "max_concurrent": self._max_concurrent[tool_name],
            "current_concurrent": self._concurrent_executions[tool_name],
        }
    
    def get_registry_stats(self) -> Dict[str, Any]:
        """
        获取注册表统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            "total_tools": len(self._tools),
            "tools": list(self._tools.keys()),
            "total_executions": sum(self._concurrent_executions.values()),
            "concurrent_executions": self._concurrent_executions.copy(),
        }


# 全局工具注册表实例
tool_registry = ToolRegistry()
