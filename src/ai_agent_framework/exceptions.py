"""
AI Agent Framework 异常定义模块

定义了框架中使用的所有异常类型，提供清晰的错误处理机制。
所有异常都继承自基础异常类，便于统一处理。
"""

from typing import Any, Dict, Optional


class AgentFrameworkError(Exception):
    """
    AI Agent Framework 基础异常类
    
    所有框架相关的异常都应该继承自这个类。
    提供了统一的错误信息格式和上下文信息。
    """
    
    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None,
    ):
        """
        初始化异常
        
        Args:
            message: 错误消息
            error_code: 错误代码，用于程序化处理
            context: 错误上下文信息
            cause: 引起此异常的原始异常
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.context = context or {}
        self.cause = cause
    
    def __str__(self) -> str:
        """返回格式化的错误信息"""
        parts = [self.message]
        
        if self.error_code:
            parts.append(f"错误代码: {self.error_code}")
            
        if self.context:
            context_str = ", ".join(f"{k}={v}" for k, v in self.context.items())
            parts.append(f"上下文: {context_str}")
            
        if self.cause:
            parts.append(f"原因: {self.cause}")
            
        return " | ".join(parts)


class ModelError(AgentFrameworkError):
    """模型相关异常"""
    
    def __init__(
        self,
        message: str,
        model_name: Optional[str] = None,
        error_code: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None,
    ):
        context = context or {}
        if model_name:
            context["model_name"] = model_name
        super().__init__(message, error_code, context, cause)


class ModelConnectionError(ModelError):
    """模型连接异常"""
    pass


class ModelAuthenticationError(ModelError):
    """模型认证异常"""
    pass


class ModelRateLimitError(ModelError):
    """模型速率限制异常"""
    pass


class ModelResponseError(ModelError):
    """模型响应异常"""
    pass


class ToolError(AgentFrameworkError):
    """工具相关异常"""
    
    def __init__(
        self,
        message: str,
        tool_name: Optional[str] = None,
        error_code: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None,
    ):
        context = context or {}
        if tool_name:
            context["tool_name"] = tool_name
        super().__init__(message, error_code, context, cause)


class ToolNotFoundError(ToolError):
    """工具未找到异常"""
    pass


class ToolExecutionError(ToolError):
    """工具执行异常"""
    pass


class ToolTimeoutError(ToolError):
    """工具执行超时异常"""
    pass


class ToolPermissionError(ToolError):
    """工具权限异常"""
    pass


class MemoryError(AgentFrameworkError):
    """记忆相关异常"""
    
    def __init__(
        self,
        message: str,
        memory_type: Optional[str] = None,
        error_code: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None,
    ):
        context = context or {}
        if memory_type:
            context["memory_type"] = memory_type
        super().__init__(message, error_code, context, cause)


class MemoryStorageError(MemoryError):
    """记忆存储异常"""
    pass


class MemoryRetrievalError(MemoryError):
    """记忆检索异常"""
    pass


class ConfigurationError(AgentFrameworkError):
    """配置相关异常"""
    pass


class ValidationError(AgentFrameworkError):
    """数据验证异常"""
    pass


class AgentError(AgentFrameworkError):
    """Agent相关异常"""
    
    def __init__(
        self,
        message: str,
        agent_id: Optional[str] = None,
        error_code: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None,
    ):
        context = context or {}
        if agent_id:
            context["agent_id"] = agent_id
        super().__init__(message, error_code, context, cause)


class AgentInitializationError(AgentError):
    """Agent初始化异常"""
    pass


class AgentExecutionError(AgentError):
    """Agent执行异常"""
    pass


class AgentStateError(AgentError):
    """Agent状态异常"""
    pass


class PromptError(AgentFrameworkError):
    """Prompt系统错误基类"""

    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        cause: Optional[Exception] = None,
    ):
        super().__init__(message, error_code, cause)


class PromptTemplateError(PromptError):
    """Prompt模板错误"""

    def __init__(
        self,
        message: str,
        template_name: Optional[str] = None,
        error_code: Optional[str] = None,
        cause: Optional[Exception] = None,
    ):
        super().__init__(message, error_code, cause)
        self.template_name = template_name


class PromptRenderError(PromptError):
    """Prompt渲染错误"""

    def __init__(
        self,
        message: str,
        template_name: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
        error_code: Optional[str] = None,
        cause: Optional[Exception] = None,
    ):
        super().__init__(message, error_code, cause)
        self.template_name = template_name
        self.context = context
