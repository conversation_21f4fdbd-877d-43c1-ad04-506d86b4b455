"""
AI Agent Framework 示例工具实现

提供一些常用的示例工具，展示如何实现ToolInterface接口。
包括计算器、天气查询、搜索等工具。
"""

import asyncio
import json
import math
import random
from typing import Any, Dict

from ai_agent_framework.core.interfaces import ToolInterface
from ai_agent_framework.core.messages import ToolResult
from ai_agent_framework.utils.logging_system import logging_system


class CalculatorTool(ToolInterface):
    """
    计算器工具
    
    支持基础数学运算，包括加减乘除、幂运算、三角函数等。
    """
    
    def __init__(self):
        """初始化计算器工具"""
        self._logger = logging_system.get_logger("calculator_tool")
    
    @property
    def name(self) -> str:
        """工具名称"""
        return "calculator"
    
    @property
    def description(self) -> str:
        """工具描述"""
        return "执行数学计算，支持基础运算、幂运算、三角函数等"
    
    @property
    def parameters_schema(self) -> Dict[str, Any]:
        """工具参数JSON Schema"""
        return {
            "type": "object",
            "properties": {
                "expression": {
                    "type": "string",
                    "description": "要计算的数学表达式，例如: '2 + 3 * 4', 'sin(pi/2)', 'sqrt(16)'"
                }
            },
            "required": ["expression"]
        }
    
    async def execute(
        self,
        arguments: Dict[str, Any],
        context: Dict[str, Any] = None,
    ) -> ToolResult:
        """
        执行计算
        
        Args:
            arguments: 工具参数
            context: 执行上下文
            
        Returns:
            ToolResult: 计算结果
        """
        try:
            expression = arguments.get("expression", "")
            if not expression:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缺少计算表达式"
                )
            
            # 安全的数学表达式计算
            result = self._safe_eval(expression)
            
            self._logger.info(f"计算表达式 '{expression}' = {result}")
            
            return ToolResult(
                tool_call_id="",
                success=True,
                result=result
            )
            
        except Exception as e:
            self._logger.error(f"计算失败: {str(e)}")
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"计算错误: {str(e)}"
            )
    
    async def validate_arguments(self, arguments: Dict[str, Any]) -> bool:
        """验证参数"""
        return "expression" in arguments and isinstance(arguments["expression"], str)
    
    def _safe_eval(self, expression: str) -> float:
        """
        安全地计算数学表达式
        
        Args:
            expression: 数学表达式
            
        Returns:
            float: 计算结果
        """
        # 允许的函数和常量
        allowed_names = {
            "abs": abs,
            "round": round,
            "min": min,
            "max": max,
            "sum": sum,
            "pow": pow,
            "sqrt": math.sqrt,
            "sin": math.sin,
            "cos": math.cos,
            "tan": math.tan,
            "asin": math.asin,
            "acos": math.acos,
            "atan": math.atan,
            "log": math.log,
            "log10": math.log10,
            "exp": math.exp,
            "pi": math.pi,
            "e": math.e,
        }
        
        # 编译表达式
        code = compile(expression, "<string>", "eval")
        
        # 检查是否只使用了允许的名称
        for name in code.co_names:
            if name not in allowed_names:
                raise ValueError(f"不允许使用的函数或变量: {name}")
        
        # 计算结果
        result = eval(code, {"__builtins__": {}}, allowed_names)
        
        return float(result)


class WeatherTool(ToolInterface):
    """
    天气查询工具
    
    模拟天气查询功能，返回指定城市的天气信息。
    """
    
    def __init__(self):
        """初始化天气工具"""
        self._logger = logging_system.get_logger("weather_tool")
    
    @property
    def name(self) -> str:
        """工具名称"""
        return "weather"
    
    @property
    def description(self) -> str:
        """工具描述"""
        return "查询指定城市的天气信息"
    
    @property
    def parameters_schema(self) -> Dict[str, Any]:
        """工具参数JSON Schema"""
        return {
            "type": "object",
            "properties": {
                "city": {
                    "type": "string",
                    "description": "要查询天气的城市名称"
                },
                "unit": {
                    "type": "string",
                    "enum": ["celsius", "fahrenheit"],
                    "description": "温度单位，默认为摄氏度",
                    "default": "celsius"
                }
            },
            "required": ["city"]
        }
    
    async def execute(
        self,
        arguments: Dict[str, Any],
        context: Dict[str, Any] = None,
    ) -> ToolResult:
        """
        执行天气查询
        
        Args:
            arguments: 工具参数
            context: 执行上下文
            
        Returns:
            ToolResult: 天气信息
        """
        try:
            city = arguments.get("city", "")
            unit = arguments.get("unit", "celsius")
            
            if not city:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缺少城市名称"
                )
            
            # 模拟API调用延迟
            await asyncio.sleep(0.5)
            
            # 生成模拟天气数据
            weather_data = self._generate_mock_weather(city, unit)
            
            self._logger.info(f"查询城市 '{city}' 的天气信息")
            
            return ToolResult(
                tool_call_id="",
                success=True,
                result=weather_data
            )
            
        except Exception as e:
            self._logger.error(f"天气查询失败: {str(e)}")
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"天气查询错误: {str(e)}"
            )
    
    async def validate_arguments(self, arguments: Dict[str, Any]) -> bool:
        """验证参数"""
        return "city" in arguments and isinstance(arguments["city"], str)
    
    def _generate_mock_weather(self, city: str, unit: str) -> Dict[str, Any]:
        """
        生成模拟天气数据
        
        Args:
            city: 城市名称
            unit: 温度单位
            
        Returns:
            Dict[str, Any]: 天气数据
        """
        # 模拟天气条件
        conditions = ["晴朗", "多云", "阴天", "小雨", "中雨", "雷阵雨"]
        condition = random.choice(conditions)
        
        # 生成温度（摄氏度）
        temp_celsius = random.randint(-10, 35)
        
        if unit == "fahrenheit":
            temperature = temp_celsius * 9/5 + 32
            temp_unit = "°F"
        else:
            temperature = temp_celsius
            temp_unit = "°C"
        
        return {
            "city": city,
            "condition": condition,
            "temperature": temperature,
            "unit": temp_unit,
            "humidity": random.randint(30, 90),
            "wind_speed": random.randint(0, 20),
            "description": f"{city}当前天气{condition}，温度{temperature}{temp_unit}"
        }


class SearchTool(ToolInterface):
    """
    搜索工具
    
    模拟网络搜索功能，返回相关的搜索结果。
    """
    
    def __init__(self):
        """初始化搜索工具"""
        self._logger = logging_system.get_logger("search_tool")
    
    @property
    def name(self) -> str:
        """工具名称"""
        return "search"
    
    @property
    def description(self) -> str:
        """工具描述"""
        return "在网络上搜索相关信息"
    
    @property
    def parameters_schema(self) -> Dict[str, Any]:
        """工具参数JSON Schema"""
        return {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "搜索关键词或问题"
                },
                "limit": {
                    "type": "integer",
                    "description": "返回结果数量限制",
                    "default": 5,
                    "minimum": 1,
                    "maximum": 10
                }
            },
            "required": ["query"]
        }
    
    async def execute(
        self,
        arguments: Dict[str, Any],
        context: Dict[str, Any] = None,
    ) -> ToolResult:
        """
        执行搜索
        
        Args:
            arguments: 工具参数
            context: 执行上下文
            
        Returns:
            ToolResult: 搜索结果
        """
        try:
            query = arguments.get("query", "")
            limit = arguments.get("limit", 5)
            
            if not query:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缺少搜索关键词"
                )
            
            # 模拟搜索延迟
            await asyncio.sleep(1.0)
            
            # 生成模拟搜索结果
            search_results = self._generate_mock_results(query, limit)
            
            self._logger.info(f"搜索关键词 '{query}'，返回 {len(search_results)} 个结果")
            
            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "query": query,
                    "results": search_results,
                    "total": len(search_results)
                }
            )
            
        except Exception as e:
            self._logger.error(f"搜索失败: {str(e)}")
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"搜索错误: {str(e)}"
            )
    
    async def validate_arguments(self, arguments: Dict[str, Any]) -> bool:
        """验证参数"""
        return "query" in arguments and isinstance(arguments["query"], str)
    
    def _generate_mock_results(self, query: str, limit: int) -> list:
        """
        生成模拟搜索结果
        
        Args:
            query: 搜索关键词
            limit: 结果数量限制
            
        Returns:
            list: 搜索结果列表
        """
        results = []
        
        for i in range(min(limit, 5)):
            results.append({
                "title": f"关于'{query}'的搜索结果 {i+1}",
                "url": f"https://example.com/result-{i+1}",
                "snippet": f"这是关于{query}的详细信息。包含了相关的背景知识和实用信息。",
                "relevance": random.uniform(0.7, 1.0)
            })
        
        return results
