"""
AI Agent Framework - 模型无关通用AI Agent框架

这是一个支持多种AI模型适配、工具调用、记忆管理等核心功能的通用AI Agent框架。
框架采用分层架构设计，遵循SOLID原则，提供灵活的扩展能力。

主要特性:
- 模型无关：支持OpenAI、Claude、国产模型等多种AI模型
- 工具调用：内置工具注册表和执行系统，支持并发执行
- 记忆管理：多层次记忆系统，支持短期、工作、长期记忆
- 异步支持：基于AsyncIO的高性能异步架构
- 类型安全：使用Pydantic v2进行数据验证和类型注解
- 可观测性：内置监控、日志和性能指标收集
"""

from ai_agent_framework.core.agent import Agent
from ai_agent_framework.core.config import FrameworkConfig
from ai_agent_framework.core.interfaces import (
    ModelInterface,
    ToolInterface,
    MemoryInterface,
)
from ai_agent_framework.core.messages import (
    Message,
    ToolCall,
    ModelResponse,
)
from ai_agent_framework.models import (
    <PERSON>AIAdapt<PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    Qwen<PERSON>dapter,
)
from ai_agent_framework.utils.tool_registry import ToolRegistry
from ai_agent_framework.memory import MemoryManager
from ai_agent_framework.exceptions import (
    AgentFrameworkError,
    ModelError,
    ToolError,
    MemoryError,
)

__version__ = "0.1.0"
__author__ = "AI Agent Framework Team"
__email__ = "<EMAIL>"
__license__ = "MIT"

# 导出主要类和函数
__all__ = [
    # 版本信息
    "__version__",
    "__author__",
    "__email__",
    "__license__",
    
    # 核心类
    "Agent",
    "FrameworkConfig",
    
    # 接口定义
    "ModelInterface",
    "ToolInterface", 
    "MemoryInterface",
    
    # 消息类型
    "Message",
    "ToolCall",
    "ModelResponse",
    
    # 模型适配器
    "OpenAIAdapter",
    "ClaudeAdapter",
    "QwenAdapter",

    # 工具和记忆
    "ToolRegistry",
    "MemoryManager",
    
    # 异常类
    "AgentFrameworkError",
    "ModelError",
    "ToolError",
    "MemoryError",
]

# 框架初始化日志
import logging
logger = logging.getLogger(__name__)
logger.info(f"AI Agent Framework v{__version__} 已加载")
