"""
AI Agent Framework 核心Agent类

实现Agent的主要逻辑，包括决策引擎、工具执行、记忆管理等核心功能。
支持ReAct和Chain-of-Thought推理模式。
"""

import asyncio
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from uuid import uuid4

from ai_agent_framework.core.config import FrameworkConfig
from ai_agent_framework.core.interfaces import ModelInterface, ToolInterface, MemoryInterface
from ai_agent_framework.core.messages import Message, MessageRole, ModelResponse, ToolCall, ToolResult
from ai_agent_framework.exceptions import AgentError, AgentExecutionError, AgentStateError
from ai_agent_framework.utils.logging_system import logging_system
from ai_agent_framework.utils.tool_registry import tool_registry
from ai_agent_framework.memory import MemoryManager


class ReasoningMode:
    """推理模式枚举"""
    REACT = "react"  # ReAct推理模式
    COT = "chain_of_thought"  # 思维链推理模式
    DIRECT = "direct"  # 直接响应模式


class AgentState:
    """Agent状态枚举"""
    IDLE = "idle"  # 空闲状态
    THINKING = "thinking"  # 思考状态
    ACTING = "acting"  # 执行动作状态
    OBSERVING = "observing"  # 观察结果状态
    COMPLETED = "completed"  # 完成状态
    ERROR = "error"  # 错误状态


class Agent:
    """
    AI Agent核心类
    
    这是框架的核心类，负责协调模型、工具和记忆系统，
    实现智能决策和任务执行。
    """
    
    def __init__(
        self,
        agent_id: Optional[str] = None,
        config: Optional[FrameworkConfig] = None,
        model: Optional[ModelInterface] = None,
        memory: Optional[MemoryInterface] = None,
        reasoning_mode: str = ReasoningMode.REACT,
    ):
        """
        初始化Agent
        
        Args:
            agent_id: Agent唯一标识符
            config: 框架配置
            model: 模型接口实例
            memory: 记忆接口实例
            reasoning_mode: 推理模式
        """
        self.agent_id = agent_id or str(uuid4())
        self.config = config or FrameworkConfig()
        self.model = model
        self.memory = memory or MemoryManager()
        self.reasoning_mode = reasoning_mode
        
        # Agent状态
        self.state = AgentState.IDLE
        self.current_task: Optional[str] = None
        self.iteration_count = 0
        self.max_iterations = self.config.max_iterations
        self.start_time: Optional[datetime] = None
        
        # 消息历史
        self.message_history: List[Message] = []
        
        # 日志记录器
        self._logger = logging_system.get_logger(f"agent_{self.agent_id}")
        
        self._logger.info(f"Agent {self.agent_id} 已初始化，推理模式: {reasoning_mode}")
    
    async def chat(
        self,
        user_input: str,
        context: Optional[Dict[str, Any]] = None,
        tools: Optional[List[str]] = None,
    ) -> ModelResponse:
        """
        与Agent进行对话
        
        Args:
            user_input: 用户输入
            context: 对话上下文
            tools: 可用工具列表
            
        Returns:
            ModelResponse: Agent响应
            
        Raises:
            AgentError: Agent执行失败时抛出
        """
        try:
            # 检查Agent状态
            if self.state == AgentState.ERROR:
                raise AgentStateError(
                    "Agent处于错误状态，无法处理请求",
                    agent_id=self.agent_id,
                    error_code="AGENT_ERROR_STATE"
                )
            
            # 重置状态
            self.state = AgentState.THINKING
            self.iteration_count = 0
            self.start_time = datetime.now()
            self.current_task = user_input
            
            # 添加用户消息到历史
            user_message = Message(
                role=MessageRole.USER,
                content=user_input,
                metadata=context or {}
            )
            self.message_history.append(user_message)
            
            # 存储到记忆中
            if self.memory:
                await self.memory.store(
                    key=f"user_input_{datetime.now().isoformat()}",
                    value=user_input,
                    memory_type="working",
                    metadata={"type": "user_input", "timestamp": datetime.now().isoformat()}
                )
            
            # 根据推理模式执行不同的处理逻辑
            if self.reasoning_mode == ReasoningMode.REACT:
                response = await self._react_reasoning(tools)
            elif self.reasoning_mode == ReasoningMode.COT:
                response = await self._cot_reasoning(tools)
            else:
                response = await self._direct_reasoning(tools)
            
            # 添加助手响应到历史
            assistant_message = response.to_message(MessageRole.ASSISTANT)
            self.message_history.append(assistant_message)
            
            # 存储响应到记忆中
            if self.memory:
                await self.memory.store(
                    key=f"assistant_response_{datetime.now().isoformat()}",
                    value=response.content,
                    memory_type="working",
                    metadata={"type": "assistant_response", "timestamp": datetime.now().isoformat()}
                )
            
            self.state = AgentState.COMPLETED
            self._logger.info(f"Agent {self.agent_id} 完成任务处理")
            
            return response
            
        except Exception as e:
            self.state = AgentState.ERROR
            self._logger.error(f"Agent {self.agent_id} 执行失败: {str(e)}")
            raise AgentExecutionError(
                f"Agent执行失败: {str(e)}",
                agent_id=self.agent_id,
                error_code="AGENT_EXECUTION_ERROR",
                cause=e
            )
    
    async def _react_reasoning(self, tools: Optional[List[str]] = None) -> ModelResponse:
        """
        ReAct推理模式
        
        实现Reasoning and Acting的循环：
        1. Thought: 思考下一步行动
        2. Action: 执行工具调用
        3. Observation: 观察执行结果
        4. 重复直到得到最终答案
        
        Args:
            tools: 可用工具列表
            
        Returns:
            ModelResponse: 最终响应
        """
        if not self.model:
            raise AgentError(
                "未配置模型，无法执行ReAct推理",
                agent_id=self.agent_id,
                error_code="NO_MODEL_CONFIGURED"
            )
        
        # 获取可用工具
        available_tools = self._get_available_tools(tools)
        tool_schemas = tool_registry.get_tool_schemas() if available_tools else None
        
        # 构建ReAct提示词
        react_prompt = self._build_react_prompt()
        if react_prompt:
            system_message = Message(
                role=MessageRole.SYSTEM,
                content=react_prompt
            )
            messages = [system_message] + self.message_history
        else:
            messages = self.message_history
        
        while self.iteration_count < self.max_iterations:
            self.iteration_count += 1
            self._logger.info(f"ReAct推理第 {self.iteration_count} 轮")
            
            try:
                # 生成响应
                response = await self.model.generate(
                    messages=messages,
                    tools=tool_schemas
                )
                
                # 检查是否有工具调用
                if response.has_tool_calls():
                    self.state = AgentState.ACTING
                    
                    # 执行工具调用
                    tool_results = await self._execute_tools(response.tool_calls)
                    
                    # 添加助手消息（包含工具调用）
                    assistant_message = response.to_message(MessageRole.ASSISTANT)
                    messages.append(assistant_message)
                    
                    # 添加工具结果消息
                    for result in tool_results:
                        tool_message = Message(
                            role=MessageRole.TOOL,
                            content=str(result.result) if result.success else f"错误: {result.error}",
                            tool_call_id=result.tool_call_id
                        )
                        messages.append(tool_message)
                    
                    self.state = AgentState.OBSERVING
                    continue
                
                # 如果没有工具调用，说明得到了最终答案
                return response
                
            except Exception as e:
                self._logger.error(f"ReAct推理第 {self.iteration_count} 轮失败: {str(e)}")
                if self.iteration_count >= self.max_iterations:
                    raise
                continue
        
        # 达到最大迭代次数
        raise AgentExecutionError(
            f"ReAct推理达到最大迭代次数 {self.max_iterations}",
            agent_id=self.agent_id,
            error_code="MAX_ITERATIONS_EXCEEDED"
        )
    
    async def _cot_reasoning(self, tools: Optional[List[str]] = None) -> ModelResponse:
        """
        Chain-of-Thought推理模式
        
        让模型逐步思考问题，展示推理过程。
        
        Args:
            tools: 可用工具列表
            
        Returns:
            ModelResponse: 最终响应
        """
        if not self.model:
            raise AgentError(
                "未配置模型，无法执行CoT推理",
                agent_id=self.agent_id,
                error_code="NO_MODEL_CONFIGURED"
            )
        
        # 获取可用工具
        available_tools = self._get_available_tools(tools)
        tool_schemas = tool_registry.get_tool_schemas() if available_tools else None
        
        # 构建CoT提示词
        cot_prompt = self._build_cot_prompt()
        if cot_prompt:
            system_message = Message(
                role=MessageRole.SYSTEM,
                content=cot_prompt
            )
            messages = [system_message] + self.message_history
        else:
            messages = self.message_history
        
        # 生成响应
        response = await self.model.generate(
            messages=messages,
            tools=tool_schemas
        )
        
        # 如果有工具调用，执行工具
        if response.has_tool_calls():
            tool_results = await self._execute_tools(response.tool_calls)
            
            # 构建包含工具结果的最终响应
            tool_results_text = "\n".join([
                f"工具 {result.tool_call_id}: {result.result if result.success else result.error}"
                for result in tool_results
            ])
            
            final_content = f"{response.content}\n\n工具执行结果:\n{tool_results_text}"
            
            return ModelResponse(
                content=final_content,
                tool_calls=response.tool_calls,
                usage=response.usage,
                model_name=response.model_name,
                finish_reason=response.finish_reason,
                metadata=response.metadata
            )
        
        return response
    
    async def _direct_reasoning(self, tools: Optional[List[str]] = None) -> ModelResponse:
        """
        直接响应模式
        
        直接调用模型生成响应，不进行复杂的推理循环。
        
        Args:
            tools: 可用工具列表
            
        Returns:
            ModelResponse: 模型响应
        """
        if not self.model:
            raise AgentError(
                "未配置模型，无法执行直接推理",
                agent_id=self.agent_id,
                error_code="NO_MODEL_CONFIGURED"
            )
        
        # 获取可用工具
        available_tools = self._get_available_tools(tools)
        tool_schemas = tool_registry.get_tool_schemas() if available_tools else None
        
        # 生成响应
        response = await self.model.generate(
            messages=self.message_history,
            tools=tool_schemas
        )
        
        # 如果有工具调用，执行工具
        if response.has_tool_calls():
            tool_results = await self._execute_tools(response.tool_calls)
            
            # 更新响应内容
            if any(not result.success for result in tool_results):
                # 如果有工具执行失败，返回错误信息
                error_results = [r for r in tool_results if not r.success]
                error_text = "; ".join([r.error for r in error_results])
                response.content += f"\n\n工具执行出现错误: {error_text}"
        
        return response

    async def _execute_tools(self, tool_calls: List[ToolCall]) -> List[ToolResult]:
        """
        执行工具调用

        Args:
            tool_calls: 工具调用列表

        Returns:
            List[ToolResult]: 工具执行结果列表
        """
        self._logger.info(f"执行 {len(tool_calls)} 个工具调用")

        # 批量执行工具
        results = await tool_registry.execute_tools_batch(
            tool_calls=tool_calls,
            max_concurrent=self.config.max_concurrent_tools
        )

        # 记录执行结果
        for result in results:
            if result.success:
                self._logger.info(f"工具 {result.tool_call_id} 执行成功")
            else:
                self._logger.error(f"工具 {result.tool_call_id} 执行失败: {result.error}")

        return results

    def _get_available_tools(self, tools: Optional[List[str]] = None) -> List[str]:
        """
        获取可用工具列表

        Args:
            tools: 指定的工具列表

        Returns:
            List[str]: 可用工具名称列表
        """
        if tools is not None:
            return tools

        # 返回所有已注册的工具
        return tool_registry.list_tools()

    def _build_react_prompt(self) -> str:
        """
        构建ReAct推理提示词

        Returns:
            str: ReAct提示词
        """
        return """你是一个智能助手，使用ReAct（Reasoning and Acting）方法来解决问题。

请按照以下格式进行推理：

思考: [分析问题，思考下一步行动]
行动: [如果需要使用工具，调用相应的工具]
观察: [观察工具执行结果]
思考: [基于观察结果继续思考]
...
最终答案: [给出最终答案]

重要规则：
1. 每次只能执行一个行动
2. 必须基于观察结果进行下一步思考
3. 如果工具执行失败，需要分析原因并尝试其他方法
4. 当有足够信息回答问题时，给出最终答案

现在开始解决用户的问题。"""

    def _build_cot_prompt(self) -> str:
        """
        构建Chain-of-Thought推理提示词

        Returns:
            str: CoT提示词
        """
        return """你是一个智能助手，请使用逐步推理的方法来解决问题。

请按照以下步骤进行思考：
1. 理解问题：明确用户想要什么
2. 分析问题：分解问题的各个部分
3. 制定计划：确定解决问题的步骤
4. 执行计划：逐步执行，如需要可以使用工具
5. 验证结果：检查答案是否合理
6. 总结答案：给出清晰的最终答案

请在回答中展示你的思考过程，让用户了解你是如何得出答案的。"""

    def get_state(self) -> Dict[str, Any]:
        """
        获取Agent当前状态

        Returns:
            Dict[str, Any]: Agent状态信息
        """
        return {
            "agent_id": self.agent_id,
            "state": self.state,
            "reasoning_mode": self.reasoning_mode,
            "current_task": self.current_task,
            "iteration_count": self.iteration_count,
            "max_iterations": self.max_iterations,
            "message_count": len(self.message_history),
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "has_model": self.model is not None,
            "has_memory": self.memory is not None,
        }

    def reset(self) -> None:
        """重置Agent状态"""
        self.state = AgentState.IDLE
        self.current_task = None
        self.iteration_count = 0
        self.start_time = None
        self.message_history.clear()

        self._logger.info(f"Agent {self.agent_id} 状态已重置")

    async def get_memory_summary(self) -> Optional[Dict[str, Any]]:
        """
        获取记忆摘要

        Returns:
            Optional[Dict[str, Any]]: 记忆摘要信息
        """
        if not self.memory:
            return None

        try:
            return await self.memory.get_stats()
        except Exception as e:
            self._logger.error(f"获取记忆摘要失败: {str(e)}")
            return None

    async def clear_memory(self, memory_type: Optional[str] = None) -> bool:
        """
        清空记忆

        Args:
            memory_type: 记忆类型，None表示清空所有

        Returns:
            bool: 是否成功
        """
        if not self.memory:
            return False

        try:
            return await self.memory.clear(memory_type)
        except Exception as e:
            self._logger.error(f"清空记忆失败: {str(e)}")
            return False
