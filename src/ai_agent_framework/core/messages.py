"""
AI Agent Framework 消息类型定义模块

定义了框架中使用的统一消息格式，确保不同模型适配器之间的兼容性。
所有消息类型都使用Pydantic进行数据验证和序列化。
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union
from uuid import uuid4

from pydantic import BaseModel, Field, validator


class MessageRole(str, Enum):
    """消息角色枚举"""
    SYSTEM = "system"      # 系统消息，用于设置AI的行为和上下文
    USER = "user"          # 用户消息，来自人类用户的输入
    ASSISTANT = "assistant"  # AI助手消息，AI生成的响应
    TOOL = "tool"          # 工具消息，工具执行的结果


class MessageType(str, Enum):
    """消息类型枚举"""
    TEXT = "text"          # 纯文本消息
    IMAGE = "image"        # 图像消息
    AUDIO = "audio"        # 音频消息
    VIDEO = "video"        # 视频消息
    FILE = "file"          # 文件消息
    TOOL_CALL = "tool_call"  # 工具调用消息
    TOOL_RESULT = "tool_result"  # 工具结果消息


class Message(BaseModel):
    """
    统一消息格式
    
    这是框架中所有消息的基础类型，提供了统一的消息结构。
    支持文本、图像、工具调用等多种消息类型。
    """
    
    id: str = Field(default_factory=lambda: str(uuid4()), description="消息唯一标识符")
    role: MessageRole = Field(..., description="消息角色")
    content: str = Field(..., description="消息内容")
    message_type: MessageType = Field(default=MessageType.TEXT, description="消息类型")
    timestamp: datetime = Field(default_factory=datetime.now, description="消息时间戳")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="消息元数据")
    
    # 可选字段
    tool_calls: Optional[List["ToolCall"]] = Field(default=None, description="工具调用列表")
    tool_call_id: Optional[str] = Field(default=None, description="关联的工具调用ID")
    
    class Config:
        """Pydantic配置"""
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
        }
    
    @validator('content')
    def validate_content(cls, v):
        """验证消息内容不能为空"""
        if not v or not v.strip():
            raise ValueError("消息内容不能为空")
        return v.strip()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return self.dict(exclude_none=True)
    
    def is_from_user(self) -> bool:
        """判断是否为用户消息"""
        return self.role == MessageRole.USER
    
    def is_from_assistant(self) -> bool:
        """判断是否为助手消息"""
        return self.role == MessageRole.ASSISTANT
    
    def has_tool_calls(self) -> bool:
        """判断是否包含工具调用"""
        return self.tool_calls is not None and len(self.tool_calls) > 0


class ToolCall(BaseModel):
    """
    工具调用格式
    
    表示AI模型请求调用某个工具的信息。
    包含工具名称、参数和调用ID等信息。
    """
    
    id: str = Field(default_factory=lambda: str(uuid4()), description="工具调用唯一标识符")
    name: str = Field(..., description="工具名称")
    arguments: Dict[str, Any] = Field(default_factory=dict, description="工具参数")
    timestamp: datetime = Field(default_factory=datetime.now, description="调用时间戳")
    
    class Config:
        """Pydantic配置"""
        json_encoders = {
            datetime: lambda v: v.isoformat(),
        }
    
    @validator('name')
    def validate_name(cls, v):
        """验证工具名称"""
        if not v or not v.strip():
            raise ValueError("工具名称不能为空")
        return v.strip()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return self.dict()


class ToolResult(BaseModel):
    """
    工具执行结果
    
    表示工具执行后的结果信息。
    包含执行状态、结果数据和错误信息等。
    """
    
    tool_call_id: str = Field(..., description="关联的工具调用ID")
    success: bool = Field(..., description="执行是否成功")
    result: Any = Field(default=None, description="执行结果数据")
    error: Optional[str] = Field(default=None, description="错误信息")
    execution_time: Optional[float] = Field(default=None, description="执行时间（秒）")
    timestamp: datetime = Field(default_factory=datetime.now, description="结果时间戳")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="结果元数据")
    
    class Config:
        """Pydantic配置"""
        json_encoders = {
            datetime: lambda v: v.isoformat(),
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return self.dict(exclude_none=True)
    
    def is_successful(self) -> bool:
        """判断执行是否成功"""
        return self.success


class ModelUsage(BaseModel):
    """
    模型使用统计
    
    记录模型调用的token使用情况和成本信息。
    """
    
    prompt_tokens: int = Field(default=0, description="输入token数量")
    completion_tokens: int = Field(default=0, description="输出token数量")
    total_tokens: int = Field(default=0, description="总token数量")
    cost: Optional[float] = Field(default=None, description="调用成本")
    
    @validator('total_tokens', always=True)
    def calculate_total_tokens(cls, v, values):
        """自动计算总token数量"""
        if v == 0:
            return values.get('prompt_tokens', 0) + values.get('completion_tokens', 0)
        return v


class ModelResponse(BaseModel):
    """
    统一模型响应格式
    
    这是所有模型适配器返回的统一响应格式。
    包含生成的内容、工具调用、使用统计等信息。
    """
    
    id: str = Field(default_factory=lambda: str(uuid4()), description="响应唯一标识符")
    content: str = Field(default="", description="生成的文本内容")
    tool_calls: List[ToolCall] = Field(default_factory=list, description="工具调用列表")
    usage: ModelUsage = Field(default_factory=ModelUsage, description="使用统计")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="响应元数据")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间戳")
    
    # 模型相关信息
    model_name: Optional[str] = Field(default=None, description="使用的模型名称")
    finish_reason: Optional[str] = Field(default=None, description="完成原因")
    
    class Config:
        """Pydantic配置"""
        json_encoders = {
            datetime: lambda v: v.isoformat(),
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return self.dict(exclude_none=True)
    
    def has_content(self) -> bool:
        """判断是否有文本内容"""
        return bool(self.content and self.content.strip())
    
    def has_tool_calls(self) -> bool:
        """判断是否有工具调用"""
        return len(self.tool_calls) > 0
    
    def to_message(self, role: MessageRole = MessageRole.ASSISTANT) -> Message:
        """转换为Message对象"""
        return Message(
            role=role,
            content=self.content,
            tool_calls=self.tool_calls if self.tool_calls else None,
            metadata={
                "model_name": self.model_name,
                "finish_reason": self.finish_reason,
                "usage": self.usage.dict(),
                **self.metadata
            }
        )


# 更新前向引用
Message.model_rebuild()
ToolCall.model_rebuild()
