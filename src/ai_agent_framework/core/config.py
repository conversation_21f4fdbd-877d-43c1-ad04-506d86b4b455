"""
AI Agent Framework 配置管理模块

提供统一的配置管理功能，支持从环境变量、配置文件等多种来源加载配置。
使用Pydantic进行配置验证和类型检查。
"""

import os
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field, validator
from pydantic_settings import BaseSettings


class ModelConfig(BaseModel):
    """
    模型配置
    
    定义单个AI模型的配置参数。
    """
    
    name: str = Field(..., description="模型名称")
    provider: str = Field(..., description="模型提供商 (openai, anthropic, qwen等)")
    api_key: Optional[str] = Field(default=None, description="API密钥")
    api_base: Optional[str] = Field(default=None, description="API基础URL")
    model_id: str = Field(..., description="模型ID")
    max_tokens: int = Field(default=4096, description="最大token数")
    temperature: float = Field(default=0.7, description="温度参数")
    timeout: float = Field(default=60.0, description="请求超时时间（秒）")
    max_retries: int = Field(default=3, description="最大重试次数")
    supports_tools: bool = Field(default=True, description="是否支持工具调用")
    supports_streaming: bool = Field(default=True, description="是否支持流式响应")
    
    # 模型特定参数
    extra_params: Dict[str, Any] = Field(default_factory=dict, description="额外参数")
    
    @validator('temperature')
    def validate_temperature(cls, v):
        """验证温度参数范围"""
        if not 0.0 <= v <= 2.0:
            raise ValueError("温度参数必须在0.0到2.0之间")
        return v
    
    @validator('max_tokens')
    def validate_max_tokens(cls, v):
        """验证最大token数"""
        if v <= 0:
            raise ValueError("最大token数必须大于0")
        return v


class ToolConfig(BaseModel):
    """
    工具配置
    
    定义工具的配置参数。
    """
    
    name: str = Field(..., description="工具名称")
    enabled: bool = Field(default=True, description="是否启用")
    timeout: Optional[float] = Field(default=None, description="执行超时时间（秒）")
    requires_confirmation: bool = Field(default=False, description="是否需要用户确认")
    max_concurrent: int = Field(default=1, description="最大并发执行数")
    
    # 工具特定配置
    config: Dict[str, Any] = Field(default_factory=dict, description="工具特定配置")


class MemoryConfig(BaseModel):
    """
    记忆配置
    
    定义记忆管理系统的配置参数。
    """
    
    storage_type: str = Field(default="sqlite", description="存储类型")
    storage_path: str = Field(default="memory.db", description="存储路径")
    
    # 记忆类型配置
    short_term_ttl: int = Field(default=3600, description="短期记忆TTL（秒）")
    working_ttl: int = Field(default=86400, description="工作记忆TTL（秒）")
    long_term_ttl: Optional[int] = Field(default=None, description="长期记忆TTL（秒），None表示永久")
    
    # 搜索配置
    similarity_threshold: float = Field(default=0.7, description="相似度搜索阈值")
    max_search_results: int = Field(default=10, description="最大搜索结果数")
    
    # 清理配置
    auto_cleanup: bool = Field(default=True, description="是否自动清理过期记忆")
    cleanup_interval: int = Field(default=3600, description="清理间隔（秒）")


class LoggingConfig(BaseModel):
    """
    日志配置
    
    定义日志系统的配置参数。
    """
    
    level: str = Field(default="INFO", description="日志级别")
    format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="日志格式"
    )
    file_path: Optional[str] = Field(default=None, description="日志文件路径")
    max_file_size: int = Field(default=10485760, description="最大文件大小（字节）")
    backup_count: int = Field(default=5, description="备份文件数量")
    
    # 结构化日志
    structured: bool = Field(default=True, description="是否使用结构化日志")
    json_format: bool = Field(default=False, description="是否使用JSON格式")


class MonitoringConfig(BaseModel):
    """
    监控配置
    
    定义监控系统的配置参数。
    """
    
    enabled: bool = Field(default=True, description="是否启用监控")
    metrics_port: int = Field(default=8000, description="指标端口")
    health_check_port: int = Field(default=8001, description="健康检查端口")
    
    # Prometheus配置
    prometheus_enabled: bool = Field(default=True, description="是否启用Prometheus指标")
    prometheus_prefix: str = Field(default="ai_agent", description="Prometheus指标前缀")
    
    # 性能监控
    track_performance: bool = Field(default=True, description="是否跟踪性能指标")
    track_errors: bool = Field(default=True, description="是否跟踪错误指标")


class FrameworkConfig(BaseSettings):
    """
    框架主配置类
    
    整合所有配置项，支持从环境变量和配置文件加载。
    """
    
    # 基础配置
    debug: bool = Field(default=False, description="是否启用调试模式")
    environment: str = Field(default="development", description="运行环境")
    
    # 模型配置
    models: Dict[str, ModelConfig] = Field(default_factory=dict, description="模型配置字典")
    default_model: str = Field(default="gpt-4", description="默认模型名称")
    
    # 工具配置
    tools: Dict[str, ToolConfig] = Field(default_factory=dict, description="工具配置字典")
    tool_timeout: float = Field(default=30.0, description="默认工具超时时间")
    max_concurrent_tools: int = Field(default=5, description="最大并发工具数")
    
    # 记忆配置
    memory: MemoryConfig = Field(default_factory=MemoryConfig, description="记忆配置")
    
    # 日志配置
    logging: LoggingConfig = Field(default_factory=LoggingConfig, description="日志配置")
    
    # 监控配置
    monitoring: MonitoringConfig = Field(default_factory=MonitoringConfig, description="监控配置")
    
    # Agent配置
    max_iterations: int = Field(default=10, description="最大迭代次数")
    max_execution_time: float = Field(default=300.0, description="最大执行时间（秒）")
    
    class Config:
        """Pydantic配置"""
        env_prefix = "AI_AGENT_"
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
    
    @validator('default_model')
    def validate_default_model(cls, v, values):
        """验证默认模型是否在配置中"""
        models = values.get('models', {})
        if models and v not in models:
            raise ValueError(f"默认模型 '{v}' 未在模型配置中找到")
        return v
    
    def get_model_config(self, model_name: Optional[str] = None) -> ModelConfig:
        """
        获取模型配置
        
        Args:
            model_name: 模型名称，None时使用默认模型
            
        Returns:
            ModelConfig: 模型配置
            
        Raises:
            ValueError: 模型未找到时抛出
        """
        name = model_name or self.default_model
        if name not in self.models:
            raise ValueError(f"模型 '{name}' 未在配置中找到")
        return self.models[name]
    
    def get_tool_config(self, tool_name: str) -> Optional[ToolConfig]:
        """
        获取工具配置
        
        Args:
            tool_name: 工具名称
            
        Returns:
            Optional[ToolConfig]: 工具配置，未找到时返回None
        """
        return self.tools.get(tool_name)
    
    def is_tool_enabled(self, tool_name: str) -> bool:
        """
        检查工具是否启用
        
        Args:
            tool_name: 工具名称
            
        Returns:
            bool: 是否启用
        """
        config = self.get_tool_config(tool_name)
        return config.enabled if config else False
    
    @classmethod
    def load_from_file(cls, config_path: Union[str, Path]) -> "FrameworkConfig":
        """
        从配置文件加载配置
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            FrameworkConfig: 配置实例
        """
        import json
        import yaml
        
        path = Path(config_path)
        if not path.exists():
            raise FileNotFoundError(f"配置文件不存在: {path}")
        
        with open(path, 'r', encoding='utf-8') as f:
            if path.suffix.lower() in ['.yaml', '.yml']:
                data = yaml.safe_load(f)
            elif path.suffix.lower() == '.json':
                data = json.load(f)
            else:
                raise ValueError(f"不支持的配置文件格式: {path.suffix}")
        
        return cls(**data)
    
    def save_to_file(self, config_path: Union[str, Path]) -> None:
        """
        保存配置到文件
        
        Args:
            config_path: 配置文件路径
        """
        import json
        import yaml
        
        path = Path(config_path)
        data = self.dict()
        
        with open(path, 'w', encoding='utf-8') as f:
            if path.suffix.lower() in ['.yaml', '.yml']:
                yaml.dump(data, f, default_flow_style=False, allow_unicode=True)
            elif path.suffix.lower() == '.json':
                json.dump(data, f, indent=2, ensure_ascii=False)
            else:
                raise ValueError(f"不支持的配置文件格式: {path.suffix}")


# 创建默认配置实例
def create_default_config() -> FrameworkConfig:
    """创建默认配置"""
    return FrameworkConfig(
        models={
            "gpt-4": ModelConfig(
                name="gpt-4",
                provider="openai",
                model_id="gpt-4",
                api_key=os.getenv("OPENAI_API_KEY"),
            ),
            "claude-3": ModelConfig(
                name="claude-3",
                provider="anthropic",
                model_id="claude-3-sonnet-20240229",
                api_key=os.getenv("ANTHROPIC_API_KEY"),
            ),
        },
        default_model="gpt-4",
    )
