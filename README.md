# 模型无关通用AI Agent框架

## 🎯 项目概述

这是一个**模型无关的通用AI Agent框架**，旨在解决当前AI Agent开发中的模型绑定问题。通过分层抽象架构，实现了对不同厂商大语言模型的统一支持，让开发者能够轻松构建可扩展、可维护的AI Agent应用。

## ✨ 核心特性

### 🔄 模型无关性
- **统一接口**：支持OpenAI GPT、Anthropic Claude、国产模型等
- **无缝切换**：一行代码即可切换不同模型
- **格式标准化**：自动处理不同模型的API格式差异

### 🧩 插件化架构
- **工具扩展**：简单易用的工具注册机制
- **模型适配**：新增模型适配器开发时间 < 2天
- **推理模式**：支持ReAct、Chain-of-Thought等多种推理模式

### 🧠 智能记忆管理
- **多层记忆**：短期、工作、长期记忆分层管理
- **智能检索**：基于相似度的记忆检索机制
- **上下文优化**：动态上下文长度管理

### 🛠️ 企业级特性
- **高性能**：支持100+并发Agent实例
- **可观测性**：完整的监控、日志、指标系统
- **安全可靠**：权限控制、错误处理、自动重试

## 🏗️ 架构设计

### 分层架构图

```
应用层 (Application Layer)
├── 聊天机器人
├── 工作流引擎
└── 智能助手

Agent核心层 (Agent Core Layer)
├── 决策引擎 (AgentEngine)
├── 记忆管理 (MemoryManager)
├── 上下文管理 (ContextManager)
├── 任务规划 (PlanningEngine)
├── 执行控制 (ExecutionEngine)
└── 状态管理 (StateManager)

工具抽象层 (Tool Abstraction Layer)
├── 工具注册表 (ToolRegistry)
├── 工具执行器 (ToolExecutor)
└── 结果处理器 (ResultProcessor)

模型适配层 (Model Adapter Layer)
├── OpenAI适配器 (OpenAIAdapter)
├── Claude适配器 (ClaudeAdapter)
├── 国产模型适配器 (QwenAdapter)
└── 统一模型接口 (ModelInterface)

基础设施层 (Infrastructure Layer)
├── 配置管理 (ConfigManager)
├── 日志系统 (LoggingSystem)
└── 监控系统 (MonitoringSystem)
```

## 🚀 快速开始

### 安装

```bash
# 克隆仓库
git clone https://github.com/aier/ai-agent.git
cd ai-agent

# 使用 Poetry 安装依赖
poetry install

# 或使用 pip 安装
pip install -e .
```

### 环境配置

```bash
# 设置环境变量
export OPENAI_API_KEY="your-openai-api-key"
export ANTHROPIC_API_KEY="your-anthropic-api-key"
export QWEN_API_KEY="your-qwen-api-key"
```

### 基础使用

```python
import asyncio
from ai_agent_framework import Agent
from ai_agent_framework.models import OpenAIAdapter
from ai_agent_framework.memory import MemoryManager
from ai_agent_framework.tools import CalculatorTool, WeatherTool
from ai_agent_framework.utils.tool_registry import tool_registry

async def main():
    # 注册工具
    calculator = CalculatorTool()
    weather = WeatherTool()
    tool_registry.register_tool(calculator)
    tool_registry.register_tool(weather)

    # 创建模型适配器
    model = OpenAIAdapter(
        api_key="your-openai-api-key",
        model_name="gpt-4"
    )

    # 创建记忆管理器
    memory = MemoryManager()

    # 创建Agent
    agent = Agent(
        model=model,
        memory=memory,
        reasoning_mode="react"  # ReAct推理模式
    )

    # 与Agent对话
    response = await agent.chat("请帮我计算 25 * 4，然后查询北京的天气")
    print(response.content)

    # 清理资源
    await memory.close()

# 运行
asyncio.run(main())
```

### 切换模型

```python
# 切换到Claude模型
claude_config = {
    "provider": "claude",
    "model_name": "claude-3-sonnet-20240229",
    "api_key": "your-claude-api-key"
}

# 使用相同的工具和逻辑
claude_agent = await framework.create_agent(
    model_config=claude_config,
    tools=tools
)

# 功能完全一致
result = await claude_agent.execute_task("同样的任务，不同的模型")
```

## 📚 文档结构

```
docs/
├── 01-需求分析文档.md      # 详细的功能和技术需求分析
├── 02-系统设计文档.md      # 完整的架构设计和接口定义
├── 03-功能开发计划.md      # 详细的开发任务和时间规划
├── api/                   # API参考文档
├── tutorials/             # 使用教程
├── examples/              # 示例代码
└── deployment/            # 部署指南
```

## 🛣️ 开发路线图

### 第一阶段：基础架构 (第1-2周)
- [x] 项目初始化和开发环境搭建
- [x] 核心接口定义和抽象类设计
- [x] 基础工具类实现

### 第二阶段：模型适配 (第3-4周)
- [ ] OpenAI模型适配器实现
- [ ] Claude模型适配器实现
- [ ] 国产模型适配器实现

### 第三阶段：核心引擎 (第5-6周)
- [ ] Agent决策引擎开发
- [ ] 工具执行系统实现
- [ ] 记忆管理系统开发

### 第四阶段：高级功能 (第7-8周)
- [ ] Prompt工程系统
- [ ] 监控和日志系统
- [ ] 性能优化和测试

## 🤝 贡献指南

我们欢迎社区贡献！请查看 [CONTRIBUTING.md](CONTRIBUTING.md) 了解如何参与项目开发。

### 贡献方式
- 🐛 报告Bug和问题
- 💡 提出新功能建议
- 📝 改进文档
- 🔧 提交代码修复
- 🧪 编写测试用例

## 📄 许可证

本项目采用 [MIT License](LICENSE) 开源协议。

## 🙏 致谢

感谢以下开源项目的启发和支持：
- [LangChain](https://github.com/langchain-ai/langchain) - AI应用开发框架
- [OpenAI Python SDK](https://github.com/openai/openai-python) - OpenAI API客户端
- [Anthropic SDK](https://github.com/anthropics/anthropic-sdk-python) - Claude API客户端

## 📞 联系我们

- **GitHub Issues**: [提交问题](https://github.com/your-org/ai-agent-framework/issues)
- **讨论区**: [GitHub Discussions](https://github.com/your-org/ai-agent-framework/discussions)
- **邮箱**: <EMAIL>

---

**让AI Agent开发变得简单而强大！** 🚀